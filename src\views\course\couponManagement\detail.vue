<script setup>
import { ref, reactive, onMounted, computed } from "vue";
import { useRouter, useRoute } from "vue-router";
import { ElMessage } from "element-plus";
import { couponFindById, findReceiveRecordByCouponId } from "@/api/coupon";
import { requestTo } from "@/utils/http/tool";
import { formatTime } from "@/utils/index";

defineOptions({
  name: "CouponManagementDetail"
});

const router = useRouter();
const route = useRoute();

const isInvalid = computed(() => route.query.valid === "invalid");

// 优惠券详情数据
const couponDetail = ref({});

const formatCouponType = type => {
  const types = {
    FULL_REDUCTION: "满减券",
    DISCOUNT: "折扣券",
    FIXED: "立减券"
  };
  return types[type] || "--";
};

const formatDiscountRule = ({
  couponDiscountType,
  discountAmount,
  conditionAmount
}) => {
  if (couponDiscountType === "FULL_REDUCTION") {
    return `满${conditionAmount || 0}减${discountAmount || 0}`;
  } else if (couponDiscountType === "DISCOUNT") {
    return `${discountAmount || 0}折`;
  } else if (couponDiscountType === "FIXED") {
    return `立减${discountAmount || 0}`;
  }
  return "--";
};

const formatTimeRange = (start, end) => {
  const startTime = start ? formatTime(start, "YYYY-MM-DD") : "";
  const endTime = end ? formatTime(end, "YYYY-MM-DD") : "";
  return startTime && endTime ? `${startTime} 至 ${endTime}` : "无限制";
};

// 加载优惠券详情
const loadCouponDetail = async id => {
  if (!id) return;
  const [err, result] = await requestTo(couponFindById({ id }));
  if (err) {
    ElMessage.error("获取优惠券详情失败");
    return;
  }
  if (result) {
    couponDetail.value = {
      couponName: result.name,
      couponType: formatCouponType(result.couponDiscountType),
      status: result.enabled ? "启用" : "停用",
      discountRule: formatDiscountRule(result),
      createTime: result.createdAt ? formatTime(result.createdAt) : "--",
      issueTime: formatTimeRange(
        result.distributionStartTime,
        result.distributionEndTime
      ),
      useTime: formatTimeRange(result.startTime, result.endTime),
      remarks: result.remarks || "--"
    };
    statistics.value = {
      issued: result.totalIssue || 0,
      received: result.receivedNumber || 0,
      used: result.usedNumber || 0
    };
  }
};

const detailConfig = reactive([
  {
    label1: "优惠券名称",
    prop1: "couponName",
    label2: "优惠券类型",
    prop2: "couponType"
  },
  {
    label1: "状态",
    prop1: "status",
    label2: "优惠门槛与金额",
    prop2: "discountRule"
  },
  {
    label1: "创建时间",
    prop1: "createTime",
    label2: "发放时间",
    prop2: "issueTime"
  },
  {
    label1: "备注",
    prop1: "remarks",
    label2: "使用时间",
    prop2: "useTime"
  }
]);

// 搜索表单数据
const searchForm = reactive({
  userAccount: "",
  getMethod: "",
  couponStatus: ""
});

const formatCouponSource = source => {
  const sources = {
    MANUAL: "手动派发",
    ONLINE: "在线领取"
  };
  return sources[source] || "--";
};

const formatFeeType = type => {
  const types = {
    CLASS_HOUR: "课时费",
    INSURANCE: "保险费",
    MATERIAL: "材料费",
    SERVICE: "服务费"
  };
  return types[type] || "--";
};

// 表格列配置
const columns = ref([
  {
    label: "用户账号",
    prop: "phone",
    minWidth: 120
  },
  {
    label: "昵称",
    prop: "name",
    minWidth: 100
  },
  {
    label: "领取时间",
    prop: "receiveTime",
    minWidth: 150,
    formatter: ({ receiveTime }) =>
      receiveTime ? formatTime(receiveTime) : "--"
  },
  {
    label: "使用时间",
    prop: "useTime",
    minWidth: 150,
    formatter: ({ useTime }) => (useTime ? formatTime(useTime) : "--")
  },
  {
    label: "优惠券使用类型",
    prop: "feeType",
    minWidth: 120,
    formatter: ({ feeType }) => formatFeeType(feeType)
  },
  {
    label: "优惠说明",
    prop: "coursePeriodName",
    minWidth: 150
  },
  {
    label: "获取方式",
    prop: "couponSource",
    minWidth: 100,
    formatter: ({ couponSource }) => formatCouponSource(couponSource)
  },
  {
    label: "操作",
    fixed: "right",
    width: 180,
    slot: "operation"
  }
]);

// 表格数据
const tableData = ref([]);
const loading = ref(false);

// 分页配置
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
});

// 统计数据
const statistics = ref({
  issued: 0,
  received: 0,
  used: 0
});

// 加载表格数据
const loadTableData = async () => {
  loading.value = true;
  const couponId = route.query.id;
  if (!couponId) {
    loading.value = false;
    return;
  }

  const params = {
    couponId,
    page: pagination.currentPage - 1,
    size: pagination.pageSize,
  };

  try {
    const [err, result] = await requestTo(findReceiveRecordByCouponId(params));
    if (err) {
      ElMessage.error("获取领取记录失败");
      tableData.value = [];
      pagination.total = 0;
      return;
    }
    if (result) {
      tableData.value = result.content || [];
      pagination.total = result.totalElements || 0;
    }
  } catch (error) {
    console.error("加载领取记录失败:", error);
    ElMessage.error("获取领取记录失败");
    tableData.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 搜索处理
const handleSearch = () => {
  console.log("搜索条件:", searchForm);
  loadTableData();
};

// 重置搜索
const handleReset = () => {
  Object.keys(searchForm).forEach(key => {
    searchForm[key] = "";
  });
  loadTableData();
};

// 新增优惠券
const handleAddCoupon = () => {
  router.push("/coupon/management/create");
};

// 关联订单操作
const handleRelateOrder = row => {
  ElMessage.info(`关联订单: ${row.userAccount}`);
};

// 返回
const handleBack = () => {
  router.go(-1);
};

onMounted(() => {
  // 获取路由参数中的优惠券ID
  const couponId = route.query.id;
  if (couponId) {
    loadCouponDetail(couponId);
  }

  // 加载优惠券详情和表格数据
  loadTableData();
});
</script>

<template>
  <div class="coupon-detail">
    <!-- 优惠券详情展示区域 -->
    <div class="common detail-container">
      <div class="detail-card">
        <div
          v-for="(item, index) in detailConfig"
          :key="index"
          class="detail-row"
        >
          <div class="detail-item">
            <span class="label">{{ item.label1 }}：</span>
            <span class="value">{{ couponDetail[item.prop1] }}</span>
          </div>
          <div class="detail-item">
            <span class="label">{{ item.label2 }}：</span>
            <span class="value">{{ couponDetail[item.prop2] }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 表格区域 -->
    <div class="common table-container">
      <!-- 搜索区域 -->
      <div class="search-section">
        <div class="coupon-search-container">
          <div class="search-items">
            <div class="search-item">
              <span class="search-label">用户账号: </span>
              <el-input
                v-model="searchForm.userAccount"
                placeholder="请输入用户账号"
                style="width: 200px"
                clearable
              />
            </div>
            <div class="search-item">
              <span class="search-label">获取方式: </span>
              <el-select
                v-model="searchForm.getMethod"
                placeholder="请选择获取方式"
                style="width: 150px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="在线领取" value="online" />
                <el-option label="手动派发" value="manual" />
              </el-select>
            </div>
            <div v-if="!isInvalid" class="search-item">
              <span class="search-label">优惠券状态: </span>
              <el-select
                v-model="searchForm.couponStatus"
                placeholder="请选择状态"
                style="width: 150px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="未使用" value="unused" />
                <el-option label="已使用" value="used" />
                <el-option label="已过期" value="expired" />
              </el-select>
            </div>
          </div>
          <div class="search-buttons">
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button
              type="primary"
              @click="handleAddCoupon"
            >
              新增优惠券
            </el-button>
          </div>
        </div>
      </div>

      <!-- 标题和统计信息 -->
      <div class="title-statistics">
        <div class="table-title">
          <h3>领取记录</h3>
        </div>
        <div class="statistics">
          <span class="stat-item">发放数量：{{ statistics.issued }}</span>
          <span class="stat-item">领取数量：{{ statistics.received }}</span>
          <span class="stat-item">使用数量：{{ statistics.used }}</span>
        </div>
      </div>

      <!-- 表格 -->
      <div class="table-section">
        <pure-table
          ref="tableRef"
          row-key="id"
          adaptive
          :adaptiveConfig="{ offsetBottom: 180 }"
          align-whole="left"
          table-layout="auto"
          :data="tableData"
          :columns="columns"
          :loading="loading"
          :pagination="pagination"
          :header-cell-style="{
            background: 'var(--el-fill-color-light)',
            color: 'var(--el-text-color-primary)'
          }"
          @page-size-change="loadTableData"
          @page-current-change="loadTableData"
        >
          <template #operation="{ row }">
            <div class="operation-buttons">
              <el-button
                class="reset-margin"
                link
                type="primary"
                @click="handleRelateOrder(row)"
              >
                关联订单
              </el-button>
            </div>
          </template>
        </pure-table>
      </div>

      <!-- 返回按钮 -->
      <div class="footer-actions">
        <el-button @click="handleBack">返回</el-button>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.coupon-detail {
  height: 88vh;
  display: flex;
  flex-direction: column;
  .common {
    padding: 20px;
    background-color: #fff;
    margin-bottom: 20px;

    &.table-container {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .detail-card {
    padding: 4px 0;
  }

  .detail-row {
    display: flex;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .detail-item {
    flex: 1;
    display: flex;
    align-items: center;

    .label {
      color: #606266;
      font-size: 14px;
      min-width: 120px;
      flex-shrink: 0;
    }

    .value {
      color: #303133;
      font-size: 14px;
      font-weight: 500;
    }
  }

  .search-section {
    margin-bottom: 20px;
    flex-shrink: 0;
  }

  .coupon-search-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search-label {
      margin-right: 14px;
      font-size: 14px;
      font-weight: 700;
      color: rgb(96, 98, 102);
    }
  }

  .search-items {
    display: flex;
    gap: 20px;
    align-items: center;
  }

  .search-item {
    display: flex;
    align-items: center;
    gap: 8px;

    .search-label {
      font-size: 14px;
      color: #606266;
      white-space: nowrap;
    }
  }

  .title-statistics {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 12px;
    border-bottom: 1px solid #e4e7ed;
    flex-shrink: 0;
  }

  .table-title {
    h3 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      color: #303133;
    }
  }

  .statistics {
    display: flex;
    gap: 40px;

    .stat-item {
      font-size: 14px;
      color: #303133;
      font-weight: 500;
    }
  }

  .table-section {
    flex: 1;
    overflow: hidden;
  }

  .operation-buttons {
    display: flex;
    gap: 8px;
  }

  .footer-actions {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
}
</style>
